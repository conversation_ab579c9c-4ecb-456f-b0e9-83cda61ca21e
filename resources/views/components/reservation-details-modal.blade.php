{{--
    Reservation Details Modal Component
    
    A reusable modal component for displaying reservation details with AJAX loading.
    
    Features:
    - AJAX data loading with loading states
    - Error handling
    - Responsive design (modal-xl)
    - Dark mode support
    - Action buttons based on user permissions
    - Scrollable content for long details
    
    Usage:
    <x-reservation-details-modal />
    
    JavaScript Requirements:
    - Bootstrap 5 modal functionality
    - reservation-modal.js for modal functions
    
    The modal is controlled via JavaScript functions:
    - openReservationModal(reservationId) - Opens modal and loads data
    - renderReservationDetails(data) - Renders reservation data
    - updateModalFooter(data) - Updates action buttons
--}}

<!-- Reservation Details Modal -->
<div class="modal fade effect-scale" id="reservationModal" tabindex="-1" aria-labelledby="reservationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reservationModalLabel">
                    <i class="ti ti-calendar-event me-2"></i>Reservation Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="reservationModalBody">
                <!-- Loading state -->
                <div id="modalLoading" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading reservation details...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading reservation details...</p>
                </div>
                
                <!-- Error state -->
                <div id="modalError" class="alert alert-danger d-none">
                    <h6 class="fw-semibold">Error Loading Reservation</h6>
                    <p class="mb-0">Failed to load reservation details. Please try again.</p>
                </div>
                
                <!-- Content will be loaded here -->
                <div id="modalContent" class="d-none"></div>
            </div>
            <div class="modal-footer" id="reservationModalFooter">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i>Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Dark Mode Styling -->
<style>
    /* Modal dark mode styling */
    [data-theme-mode="dark"] .modal-content {
        background-color: rgb(var(--body-bg-rgb2)) !important;
        border-color: rgba(255,255,255,0.1) !important;
    }

    [data-theme-mode="dark"] .modal-header {
        border-color: rgba(255,255,255,0.1) !important;
    }

    [data-theme-mode="dark"] .modal-footer {
        border-color: rgba(255,255,255,0.1) !important;
    }

    [data-theme-mode="dark"] .modal-title {
        color: rgba(255,255,255,0.9) !important;
    }

    /* Ensure modal cards are properly styled in dark mode */
    [data-theme-mode="dark"] #reservationModal .card {
        background-color: rgb(var(--light-rgb)) !important;
        border-color: rgba(255,255,255,0.1) !important;
    }

    [data-theme-mode="dark"] #reservationModal .card-header {
        background-color: rgb(var(--light-rgb)) !important;
        border-color: rgba(255,255,255,0.1) !important;
    }

    [data-theme-mode="dark"] #reservationModal .table {
        color: rgba(255,255,255,0.9) !important;
    }

    [data-theme-mode="dark"] #reservationModal .table-light {
        background-color: rgba(255,255,255,0.1) !important;
    }
</style>
